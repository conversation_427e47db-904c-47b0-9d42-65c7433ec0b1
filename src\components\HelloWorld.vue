<script setup>
// 移除不需要的响应式数据和props
</script>

<template>
  <div class="container">
    <div class="header">
      <h1>网站建设中</h1>
      <p>Website Under Construction</p>
    </div>

    <div class="content">
      <div class="notice">
        <h3>网站说明</h3>
        <p>本网站正在建设中，暂时无法提供服务。网站建设完成后将为您提供更好的浏览体验。</p>
        <p>感谢您的理解与支持！</p>
      </div>

      <div class="website-info">
        <h3>网站基本信息</h3>
        <div class="info-item">
          <strong>网站名称：</strong>个人网站
        </div>
        <div class="info-item">
          <strong>网站性质：</strong>个人非经营性网站
        </div>
        <div class="info-item">
          <strong>网站用途：</strong>个人学习交流、技术分享
        </div>
        <div class="info-item">
          <strong>网站内容：</strong>个人学习笔记、技术文章分享（非商业用途）
        </div>
        <div class="info-item">
          <strong>网站所有者：</strong>个人
        </div>
        <div class="info-item">
          <strong>备注说明：</strong>本网站为个人非经营性网站，仅用于个人学习和技术交流，不涉及任何商业活动
        </div>
      </div>

      <div class="notice">
        <h3>重要声明</h3>
        <p>本网站为个人网站，非经营性质，不用于任何商业目的。网站内容仅供学习交流使用，不涉及商品销售、服务提供等经营性活动。</p>
      </div>
    </div>

    <div class="footer">
      <p>个人非经营性网站 | 仅用于学习交流</p>
      <p>© 2024 个人网站 | 网站建设中</p>
    </div>

    <footer class="icp-footer">
      <!-- ICP 备案：必须可点击到工信部 -->
      <a href="https://beian.miit.gov.cn/" target="_blank" rel="noopener">
        粤ICP备2025XXXX号-1
      </a>
    </footer>
  </div>
</template>

<style scoped>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.container {
  max-width: 600px;
  margin: 0 auto;
  padding: 40px;
  background-color: white;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  text-align: center;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  line-height: 1.6;
  color: #333;
}

.header {
  margin-bottom: 30px;
}

.header h1 {
  color: #2c3e50;
  font-size: 2.5em;
  margin-bottom: 10px;
  font-weight: 300;
}

.header p {
  color: #7f8c8d;
  font-size: 1.2em;
}

.content {
  margin: 30px 0;
}

.notice {
  background-color: #ecf0f1;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
  border-left: 4px solid #3498db;
}

.notice h3 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.notice p {
  color: #7f8c8d;
  line-height: 1.6;
}

.website-info {
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
  text-align: left;
}

.website-info h3 {
  color: #2c3e50;
  margin-bottom: 15px;
  text-align: center;
}

.info-item {
  margin: 10px 0;
  padding: 8px 0;
  border-bottom: 1px solid #ecf0f1;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item strong {
  color: #2c3e50;
  display: inline-block;
  width: 120px;
}

.footer {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #ecf0f1;
  color: #95a5a6;
  font-size: 0.9em;
}

.icp-footer {
  text-align: center;
  padding: 16px 0;
  color: #666;
}

.icp-footer a {
  color: #666;
  text-decoration: none;
}

.icp-footer a:hover {
  text-decoration: underline;
}

@media (max-width: 600px) {
  .container {
    margin: 20px;
    padding: 30px 20px;
  }

  .header h1 {
    font-size: 2em;
  }

  .info-item strong {
    width: 100px;
    font-size: 0.9em;
  }
}
</style>
