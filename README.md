# Vue 3 + Vite Template

This template should help get you started developing with Vue 3 in Vite. The template uses Vue 3 `<script setup>` SFCs, check out the [script setup docs](https://v3.vuejs.org/api/sfc-script-setup.html#sfc-script-setup) to learn more.

While this project uses Vue.js, Vite supports many popular JS frameworks. [See all the supported frameworks](https://vitejs.dev/guide/#scaffolding-your-first-vite-project).

[Vite Configuration Reference](https://vite.dev/config/)

## Local Development

### Project Setup

```sh
npm install
```

### Compile and Hot-Reload for Development

```sh
npm run dev
# Open http://localhost:5173 with your browser to see the result.
```

### Compile and Minify for Production

```sh
npm run build
# The build artifacts will be stored in the `dist/` directory.
```

## Deploy Your Own

Deploy your own Vite project with Edgeone pages.

[![Deploy with EdgeOne Pages](https://cdnstatic.tencentcs.com/edgeone/pages/deploy.svg)](https://edgeone.ai/pages/new?template=vite-vue3)

More Templates: [EdgeOne Pages](https://edgeone.ai/pages/templates)
